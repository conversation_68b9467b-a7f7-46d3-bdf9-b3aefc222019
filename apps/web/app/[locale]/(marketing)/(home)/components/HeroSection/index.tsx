'use client'

import { useState, useCallback, useRef } from 'react'
import './hero-animations.css'
import { useRouter } from '@i18n/routing'
import { Upload, ArrowRight, Image as ImageIcon } from 'lucide-react'
import Image from 'next/image'
import { DragOverlay } from '../../../../../../components/ImageEditor/components/DragOverlay'

interface ImageData {
  id: string
  file: File
  url: string
  width: number
  height: number
  name: string
}

const PRESET_BACKGROUNDS = [
  'https://images.pexels.com/photos/********/pexels-photo-********.jpeg?auto=compress&cs=tinysrgb&w=600&loading=lazy',
  'https://images.pexels.com/photos/********/pexels-photo-********.jpeg?auto=compress&cs=tinysrgb&w=600&loading=lazy',
  'https://images.pexels.com/photos/********/pexels-photo-********.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500',
  'https://images.pexels.com/photos/********/pexels-photo-********.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500',
]

export function HeroSection() {
  const router = useRouter()
  const [isDragActive, setIsDragActive] = useState(false)

  // Global drag and drop handlers (similar to ImageEditor)
  const handleGlobalDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragActive(true)
  }, [])

  const handleGlobalDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }, [])

  const handleGlobalDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    // Only hide overlay when leaving the main container
    if (e.currentTarget === e.target) {
      setIsDragActive(false)
    }
  }, [])

  const handleGlobalDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault()
      e.stopPropagation()
      setIsDragActive(false)

      // Navigate to playground page where ImageEditor will handle the files
      router.push('/playground')
    },
    [router]
  )

  const handleUploadClick = () => {
    // Navigate to playground page where ImageEditor will handle the upload
    router.push('/playground')
  }

  const handlePresetClick = () => {
    // Navigate to playground page where ImageEditor will handle the preset
    router.push('/playground')
  }

  return (
    <>
      <div
        className="relative min-h-screen flex items-center justify-center px-4 py-16"
        onDragEnter={handleGlobalDragEnter}
        onDragOver={handleGlobalDragOver}
        onDragLeave={handleGlobalDragLeave}
        onDrop={handleGlobalDrop}
      >
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div
            className="absolute top-20 left-10 w-20 h-20 bg-yellow-200 rounded-full opacity-20 animate-pulse"
            style={{ animationDuration: '3s' }}
          ></div>
          <div
            className="absolute top-40 right-20 w-16 h-16 bg-blue-200 rounded-full opacity-30 animate-bounce"
            style={{ animationDuration: '2s' }}
          ></div>
          <div
            className="absolute bottom-32 left-1/4 w-12 h-12 bg-purple-200 rounded-full opacity-25 animate-pulse"
            style={{ animationDuration: '4s' }}
          ></div>
          <div
            className="absolute top-1/2 right-1/3 w-8 h-8 bg-pink-200 rounded-full opacity-20 animate-ping"
            style={{ animationDuration: '3s' }}
          ></div>
        </div>

        <div className="max-w-7xl mx-auto grid lg:grid-cols-2 gap-12 items-center relative z-10">
          {/* Left side - Content */}
          <div className="space-y-8 animate-fade-in-left">
            {/* Main heading with animation */}
            <div className="space-y-4">
              <h1 className="text-5xl lg:text-6xl font-bold text-gray-900 leading-tight animate-fade-in-up">
                Remove Image
                <br />
                <span className="bg-gradient-to-r from-yellow-400 via-orange-400 to-yellow-500 bg-clip-text text-transparent animate-gradient">
                  Background
                </span>
              </h1>

              <div className="flex items-center gap-2 text-lg text-gray-600 animate-fade-in-up animate-delay-200">
                <span className="font-medium">100% Automatically and</span>
                <span className="bg-yellow-400 text-yellow-900 px-3 py-1 rounded-full text-sm font-bold animate-float">
                  Free
                </span>
              </div>
            </div>

            {/* Upload button */}
            <div className="space-y-4 animate-fade-in-up animate-delay-300">
              <button
                onClick={handleUploadClick}
                className="group bg-blue-500 hover:bg-blue-600 text-white px-8 py-4 rounded-2xl text-lg font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-lg flex items-center gap-3 hover-lift"
              >
                <Upload className="w-5 h-5" />
                Upload Image
              </button>

              <p className="text-sm text-gray-500">
                or drop a file,
                <br />
                paste image or URL
              </p>
            </div>
          </div>

          {/* Right side - Upload area and presets */}
          <div className="space-y-6 animate-fade-in-right animate-delay-400">
            {/* Upload area */}
            <div
              className="group bg-white rounded-3xl border-2 border-dashed border-gray-200 p-8 text-center hover:border-blue-300 hover:bg-blue-50/30 transition-all duration-300 min-h-[300px] flex flex-col items-center justify-center cursor-pointer"
              onClick={handleUploadClick}
            >
              <div className="space-y-4">
                <div className="w-16 h-16 bg-blue-100 group-hover:bg-blue-200 rounded-full flex items-center justify-center mx-auto transition-colors duration-300">
                  <ImageIcon className="w-8 h-8 text-blue-500 group-hover:text-blue-600 transition-colors duration-300" />
                </div>
                <div>
                  <p className="text-lg font-medium text-gray-700 group-hover:text-blue-700 mb-2 transition-colors duration-300">
                    Upload Image
                  </p>
                  <p className="text-sm text-gray-500 group-hover:text-blue-600 transition-colors duration-300">
                    or drop a file,
                    <br />
                    paste image or URL
                  </p>
                </div>
              </div>
            </div>

            {/* Preset images */}
            <div className="space-y-3">
              <p className="text-sm text-gray-600 font-medium">No image?</p>
              <p className="text-sm text-gray-500">Try one of these:</p>

              <div className="grid grid-cols-4 gap-3">
                {PRESET_BACKGROUNDS.map((imageUrl, index) => (
                  <button
                    key={index}
                    onClick={handlePresetClick}
                    className="group relative aspect-square rounded-lg overflow-hidden border-2 border-transparent hover:border-blue-400 transition-all duration-300 transform hover:scale-105"
                  >
                    <Image
                      src={imageUrl}
                      alt={`Preset ${index + 1}`}
                      fill
                      className="object-cover"
                      sizes="(max-width: 768px) 25vw, 12vw"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                      <ArrowRight className="w-5 h-5 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Terms notice */}
            <div className="text-xs text-gray-400 space-y-1">
              <p>
                By uploading an image or URL you agree to our{' '}
                <a href="/terms" className="text-blue-500 hover:underline">
                  Terms of Service
                </a>
                . To learn more about how
              </p>
              <p>
                remove.bg handles your personal data, check our{' '}
                <a href="/privacy" className="text-blue-500 hover:underline">
                  Privacy Policy
                </a>
                .
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Drag overlay */}
      {isDragActive && <DragOverlay />}
    </>
  )
}
