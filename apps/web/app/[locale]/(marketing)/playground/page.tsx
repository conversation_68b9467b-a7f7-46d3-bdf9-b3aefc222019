import { ImageEditor } from '../../../../components/ImageEditor/components/ImageEditor'

export async function generateMetadata() {
  return {
    title: 'Playground - Remove Background',
    description: 'Edit and process your uploaded images',
  }
}

const PlaygroundPage = async () => {
  return (
    <div className="min-h-screen bg-gray-100">
      {/* Main Layout */}
      <div className="flex min-h-[calc(100vh-73px)]">
        {/* Left Side - Main Canvas Area */}
        <div className="flex-1 flex flex-col">
          <ImageEditor />
        </div>
      </div>
    </div>
  )
}

export default PlaygroundPage
